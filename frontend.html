<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FluxDraw - Modern Canvas</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow: hidden;
            background: black;
            color: white;
        }

        .toolbar {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(40, 40, 40, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 4px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .tool-btn {
            width: 40px;
            height: 40px;
            border: none;
            background: transparent;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            color: rgba(255, 255, 255, 0.8);
        }

        .tool-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .tool-btn.active {
            background: white;
            color: black;
        }

        .tool-btn svg {
            width: 20px;
            height: 20px;
            stroke: currentColor;
            fill: none;
            stroke-width: 2;
        }

        .divider {
            width: 1px;
            height: 24px;
            background: rgba(255, 255, 255, 0.2);
            margin: 0 4px;
        }

        .canvas-container {
            width: 100vw;
            height: 100vh;
            background: black;
        }

        .chat-sidebar {
            position: fixed;
            top: 0;
            right: 0;
            width: 20%;
            height: 100vh;
            background: rgba(40, 40, 40, 0.95);
            backdrop-filter: blur(10px);
            border-left: 1px solid rgba(255, 255, 255, 0.1);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            z-index: 999;
            display: flex;
            flex-direction: column;
        }

        .chat-sidebar.open {
            transform: translateX(0);
        }

        .chat-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .chat-input-area {
            padding: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .chat-input {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 12px;
            color: white;
            outline: none;
        }

        .chat-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .message {
            margin-bottom: 16px;
            padding: 12px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
        }

        .tooltip {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.2s;
            margin-bottom: 8px;
        }

        .tool-btn:hover .tooltip {
            opacity: 1;
        }

        #main-canvas {
            display: block;
        }

        .color-picker {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            background: transparent;
        }

        .brush-size {
            width: 60px;
            height: 40px;
            cursor: pointer;
            background: transparent;
        }
    </style>
</head>
<body>
    <!-- Toolbar -->
    <div class="toolbar">
        <button class="tool-btn" id="lock-btn" title="Lock">
            <svg viewBox="0 0 24 24"><rect x="3" y="11" width="18" height="11" rx="2" ry="2"/><path d="M7 11V7a5 5 0 0 1 10 0v4"/></svg>
            <div class="tooltip">Lock</div>
        </button>
        
        <button class="tool-btn" id="hand-btn" title="Hand">
            <svg viewBox="0 0 24 24"><path d="M18 11V6a2 2 0 0 0-4 0v5"/><path d="M14 10V4a2 2 0 0 0-4 0v2"/><path d="M10 10.5V6a2 2 0 0 0-4 0v8"/><path d="M18 8a2 2 0 1 1 4 0v6a8 8 0 0 1-8 8h-2c-2.8 0-4.5-.86-5.99-2.34l-3.6-3.6a2 2 0 0 1 2.83-2.83L7 15"/></svg>
            <div class="tooltip">Hand (H)</div>
        </button>
        
        <button class="tool-btn active" id="select-btn" title="Select">
            <svg viewBox="0 0 24 24"><path d="M3 3l7.07 16.97 2.51-7.39 7.39-2.51L3 3z"/></svg>
            <div class="tooltip">Select (V)</div>
        </button>
        
        <div class="divider"></div>
        
        <button class="tool-btn" id="rectangle-btn" title="Rectangle">
            <svg viewBox="0 0 24 24"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/></svg>
            <div class="tooltip">Rectangle (R)</div>
        </button>
        
        <button class="tool-btn" id="diamond-btn" title="Diamond">
            <svg viewBox="0 0 24 24"><path d="M6 3h12l4 6-8 12L6 9l4-6z"/></svg>
            <div class="tooltip">Diamond (D)</div>
        </button>
        
        <button class="tool-btn" id="circle-btn" title="Circle">
            <svg viewBox="0 0 24 24"><circle cx="12" cy="12" r="10"/></svg>
            <div class="tooltip">Circle (C)</div>
        </button>
        
        <button class="tool-btn" id="arrow-btn" title="Arrow">
            <svg viewBox="0 0 24 24"><path d="M5 12h14"/><path d="M12 5l7 7-7 7"/></svg>
            <div class="tooltip">Arrow (A)</div>
        </button>
        
        <button class="tool-btn" id="line-btn" title="Line">
            <svg viewBox="0 0 24 24"><path d="M5 12h14"/></svg>
            <div class="tooltip">Line (L)</div>
        </button>
        
        <div class="divider"></div>
        
        <button class="tool-btn" id="pen-btn" title="Pen">
            <svg viewBox="0 0 24 24"><path d="M12 19l7-7 3 3-7 7-3-3z"/><path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z"/><path d="M2 2l7.586 7.586"/></svg>
            <div class="tooltip">Pen (P)</div>
        </button>
        
        <button class="tool-btn" id="text-btn" title="Text">
            <svg viewBox="0 0 24 24"><polyline points="4,7 4,4 20,4 20,7"/><line x1="9" y1="20" x2="15" y2="20"/><line x1="12" y1="4" x2="12" y2="20"/></svg>
            <div class="tooltip">Text (T)</div>
        </button>
        
        <button class="tool-btn" id="image-btn" title="Image">
            <svg viewBox="0 0 24 24"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/><circle cx="8.5" cy="8.5" r="1.5"/><polyline points="21,15 16,10 5,21"/></svg>
            <div class="tooltip">Image (I)</div>
        </button>
        
        <div class="divider"></div>
        
        <!-- Color Picker -->
        <input type="color" id="color-picker" value="#ffffff" class="color-picker" title="Color">
        
        <!-- Brush Size -->
        <input type="range" id="brush-size" min="1" max="20" value="2" class="brush-size" title="Brush Size">
        
        <div class="divider"></div>
        
        <button class="tool-btn" id="chat-btn" title="AI Chat">
            <svg viewBox="0 0 24 24"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/></svg>
            <div class="tooltip">AI Chat</div>
        </button>
        
        <button class="tool-btn" id="collaborate-btn" title="Collaborate">
            <svg viewBox="0 0 24 24"><path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M23 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/></svg>
            <div class="tooltip">Collaborate</div>
        </button>
    </div>

    <!-- Canvas -->
    <div class="canvas-container">
        <canvas id="main-canvas"></canvas>
    </div>

    <!-- Chat Sidebar -->
    <div class="chat-sidebar" id="chat-sidebar">
        <div class="chat-header">
            <h3>AI Assistant</h3>
            <button class="tool-btn" id="close-chat">
                <svg viewBox="0 0 24 24"><line x1="18" y1="6" x2="6" y2="18"/><line x1="6" y1="6" x2="18" y2="18"/></svg>
            </button>
        </div>
        <div class="chat-messages" id="chat-messages">
            <div class="message">
                <strong>AI:</strong> Hello! I'm your AI assistant. I can help you with drawing, answer questions, and provide suggestions. What would you like to create today?
            </div>
        </div>
        <div class="chat-input-area">
            <input type="text" class="chat-input" id="chat-input" placeholder="Ask AI anything...">
        </div>
    </div>

    <script>
        // Initialize Fabric.js canvas
        const canvas = new fabric.Canvas('main-canvas', {
            backgroundColor: '#000000',
            selection: true,
            isDrawingMode: false
        });

        // Resize canvas to full screen
        function resizeCanvas() {
            canvas.setDimensions({
                width: window.innerWidth,
                height: window.innerHeight
            });
            canvas.renderAll();
        }

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // Current tool state
        let currentTool = 'select';
        let isDrawing = false;
        let startX, startY;
        let currentShape = null;
        let currentColor = '#ffffff';
        let currentBrushSize = 2;

        // Tool buttons
        const toolButtons = {
            'lock': document.getElementById('lock-btn'),
            'hand': document.getElementById('hand-btn'),
            'select': document.getElementById('select-btn'),
            'rectangle': document.getElementById('rectangle-btn'),
            'diamond': document.getElementById('diamond-btn'),
            'circle': document.getElementById('circle-btn'),
            'arrow': document.getElementById('arrow-btn'),
            'line': document.getElementById('line-btn'),
            'pen': document.getElementById('pen-btn'),
            'text': document.getElementById('text-btn'),
            'image': document.getElementById('image-btn'),
            'chat': document.getElementById('chat-btn'),
            'collaborate': document.getElementById('collaborate-btn')
        };

        // Color picker and brush size
        const colorPicker = document.getElementById('color-picker');
        const brushSize = document.getElementById('brush-size');

        colorPicker.addEventListener('change', (e) => {
            currentColor = e.target.value;
            if (canvas.isDrawingMode) {
                canvas.freeDrawingBrush.color = currentColor;
            }
        });

        brushSize.addEventListener('input', (e) => {
            currentBrushSize = parseInt(e.target.value);
            if (canvas.isDrawingMode) {
                canvas.freeDrawingBrush.width = currentBrushSize;
            }
        });

        // Set active tool
        function setActiveTool(tool) {
            currentTool = tool;

            // Update button states
            Object.values(toolButtons).forEach(btn => btn.classList.remove('active'));
            if (toolButtons[tool]) {
                toolButtons[tool].classList.add('active');
            }

            // Configure canvas
            switch(tool) {
                case 'select':
                    canvas.isDrawingMode = false;
                    canvas.selection = true;
                    canvas.defaultCursor = 'default';
                    break;
                case 'hand':
                    canvas.isDrawingMode = false;
                    canvas.selection = false;
                    canvas.defaultCursor = 'grab';
                    break;
                case 'pen':
                    canvas.isDrawingMode = true;
                    canvas.selection = false;
                    canvas.freeDrawingBrush.width = currentBrushSize;
                    canvas.freeDrawingBrush.color = currentColor;
                    break;
                case 'line':
                case 'rectangle':
                case 'circle':
                case 'diamond':
                case 'arrow':
                    canvas.isDrawingMode = false;
                    canvas.selection = false;
                    canvas.defaultCursor = 'crosshair';
                    break;
                case 'text':
                    canvas.isDrawingMode = false;
                    canvas.selection = false;
                    canvas.defaultCursor = 'text';
                    break;
            }
        }

        // Add tool button event listeners
        Object.entries(toolButtons).forEach(([tool, button]) => {
            button.addEventListener('click', () => {
                if (tool === 'chat') {
                    toggleChat();
                } else {
                    setActiveTool(tool);
                }
            });
        });

        // Shape drawing functions
        function startShapeDrawing(pointer) {
            isDrawing = true;
            startX = pointer.x;
            startY = pointer.y;

            switch(currentTool) {
                case 'line':
                    currentShape = new fabric.Line([startX, startY, startX, startY], {
                        stroke: currentColor,
                        strokeWidth: currentBrushSize,
                        selectable: false
                    });
                    break;
                case 'rectangle':
                    currentShape = new fabric.Rect({
                        left: startX,
                        top: startY,
                        width: 0,
                        height: 0,
                        fill: 'transparent',
                        stroke: currentColor,
                        strokeWidth: currentBrushSize,
                        selectable: false
                    });
                    break;
                case 'circle':
                    currentShape = new fabric.Circle({
                        left: startX,
                        top: startY,
                        radius: 0,
                        fill: 'transparent',
                        stroke: currentColor,
                        strokeWidth: currentBrushSize,
                        selectable: false
                    });
                    break;
                case 'diamond':
                    currentShape = new fabric.Polygon([
                        {x: 0, y: -50},
                        {x: 50, y: 0},
                        {x: 0, y: 50},
                        {x: -50, y: 0}
                    ], {
                        left: startX,
                        top: startY,
                        fill: 'transparent',
                        stroke: currentColor,
                        strokeWidth: currentBrushSize,
                        selectable: false
                    });
                    break;
            }

            if (currentShape) {
                canvas.add(currentShape);
            }
        }

        function updateShapeDrawing(pointer) {
            if (!isDrawing || !currentShape) return;

            switch(currentTool) {
                case 'line':
                    currentShape.set({ x2: pointer.x, y2: pointer.y });
                    break;
                case 'rectangle':
                    const width = pointer.x - startX;
                    const height = pointer.y - startY;
                    currentShape.set({
                        width: Math.abs(width),
                        height: Math.abs(height),
                        left: width < 0 ? pointer.x : startX,
                        top: height < 0 ? pointer.y : startY
                    });
                    break;
                case 'circle':
                    const radius = Math.sqrt(Math.pow(pointer.x - startX, 2) + Math.pow(pointer.y - startY, 2)) / 2;
                    currentShape.set({
                        radius: radius,
                        left: startX - radius,
                        top: startY - radius
                    });
                    break;
                case 'diamond':
                    const size = Math.abs(pointer.x - startX);
                    currentShape.set({
                        scaleX: size / 100,
                        scaleY: size / 100
                    });
                    break;
            }

            canvas.renderAll();
        }

        function finishShapeDrawing() {
            if (currentShape) {
                currentShape.set({ selectable: true });
            }
            isDrawing = false;
            currentShape = null;
        }

        // Text tool
        function addText(pointer) {
            const text = new fabric.IText('Type here...', {
                left: pointer.x,
                top: pointer.y,
                fontFamily: 'Arial',
                fontSize: 20,
                fill: currentColor
            });
            canvas.add(text);
            canvas.setActiveObject(text);
            text.enterEditing();
        }

        // Canvas mouse events
        canvas.on('mouse:down', (options) => {
            if (currentTool === 'text') {
                addText(options.pointer);
            } else if (['line', 'rectangle', 'circle', 'diamond', 'arrow'].includes(currentTool)) {
                startShapeDrawing(options.pointer);
            }
        });

        canvas.on('mouse:move', (options) => {
            if (isDrawing) {
                updateShapeDrawing(options.pointer);
            }
        });

        canvas.on('mouse:up', () => {
            if (isDrawing) {
                finishShapeDrawing();
            }
        });

        // Chat functionality
        const chatSidebar = document.getElementById('chat-sidebar');
        const chatMessages = document.getElementById('chat-messages');
        const chatInput = document.getElementById('chat-input');
        const closeChatBtn = document.getElementById('close-chat');

        function toggleChat() {
            chatSidebar.classList.toggle('open');
        }

        function addMessage(sender, message) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';
            messageDiv.innerHTML = `<strong>${sender}:</strong> ${message}`;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function sendMessage() {
            const message = chatInput.value.trim();
            if (!message) return;

            addMessage('You', message);
            chatInput.value = '';

            // Send to AI backend
            fetch('/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ message: message })
            })
            .then(response => response.json())
            .then(data => {
                addMessage('AI', data.response);
            })
            .catch(error => {
                console.error('Error:', error);
                addMessage('AI', 'Sorry, I encountered an error. Please try again.');
            });
        }

        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        closeChatBtn.addEventListener('click', () => {
            chatSidebar.classList.remove('open');
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.target.tagName === 'INPUT') return;

            switch(e.key.toLowerCase()) {
                case 'v': setActiveTool('select'); break;
                case 'h': setActiveTool('hand'); break;
                case 'p': setActiveTool('pen'); break;
                case 'l': setActiveTool('line'); break;
                case 'r': setActiveTool('rectangle'); break;
                case 'c': setActiveTool('circle'); break;
                case 't': setActiveTool('text'); break;
                case 'a': setActiveTool('arrow'); break;
                case 'd': setActiveTool('diamond'); break;
                case 'escape':
                    setActiveTool('select');
                    chatSidebar.classList.remove('open');
                    break;
            }
        });

        // Initialize with select tool
        setActiveTool('select');

        console.log('FluxDraw initialized successfully!');
        console.log('Available tools:', Object.keys(toolButtons));
        console.log('Keyboard shortcuts: V=Select, H=Hand, P=Pen, L=Line, R=Rectangle, C=Circle, T=Text, A=Arrow, D=Diamond');
    </script>
</body>
</html>
