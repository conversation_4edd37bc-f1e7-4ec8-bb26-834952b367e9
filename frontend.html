<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FluxDraw - AI Powered Canvas</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow: hidden;
            background: black;
            color: white;
        }

        .toolbar {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(40, 40, 40, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 4px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .tool-btn {
            width: 40px;
            height: 40px;
            border: none;
            background: transparent;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            color: rgba(255, 255, 255, 0.8);
        }

        .tool-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .tool-btn.active {
            background: white;
            color: black;
        }

        .tool-btn svg {
            width: 20px;
            height: 20px;
            stroke: currentColor;
            fill: none;
            stroke-width: 2;
        }

        .divider {
            width: 1px;
            height: 24px;
            background: rgba(255, 255, 255, 0.2);
            margin: 0 4px;
        }

        .canvas-container {
            width: 100vw;
            height: 100vh;
            background: black;
        }

        .status-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(40, 40, 40, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 12px 16px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            gap: 8px;
            z-index: 1000;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ef4444;
        }

        .status-dot.online {
            background: #22c55e;
        }

        .video-container {
            position: fixed;
            top: 80px;
            right: 20px;
            width: 300px;
            background: rgba(40, 40, 40, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 999;
            display: none;
        }

        .video-header {
            padding: 16px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .video-content {
            padding: 16px;
        }

        .video-element {
            width: 100%;
            border-radius: 8px;
            background: #1a1a1a;
            margin-bottom: 8px;
        }

        .video-controls {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-top: 12px;
        }

        .video-btn {
            width: 36px;
            height: 36px;
            border: none;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .video-btn.call {
            background: #22c55e;
            color: white;
        }

        .video-btn.end {
            background: #ef4444;
            color: white;
        }

        .video-btn.mute {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .chat-sidebar {
            position: fixed;
            top: 0;
            right: 0;
            width: 20%;
            height: 100vh;
            background: rgba(40, 40, 40, 0.95);
            backdrop-filter: blur(10px);
            border-left: 1px solid rgba(255, 255, 255, 0.1);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            z-index: 998;
            display: flex;
            flex-direction: column;
        }

        .chat-sidebar.open {
            transform: translateX(0);
        }

        .chat-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .chat-input-area {
            padding: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            gap: 8px;
        }

        .chat-input {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 12px;
            color: white;
            outline: none;
        }

        .chat-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .send-btn {
            background: #007AFF;
            border: none;
            border-radius: 8px;
            padding: 12px 16px;
            color: white;
            cursor: pointer;
            transition: background 0.2s;
        }

        .send-btn:hover {
            background: #0056CC;
        }

        .screenshot-btn {
            background: #34C759;
            border: none;
            border-radius: 8px;
            padding: 12px;
            color: white;
            cursor: pointer;
            transition: background 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .screenshot-btn:hover {
            background: #28A745;
        }

        .message {
            margin-bottom: 16px;
            padding: 12px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
        }

        .tooltip {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.2s;
            margin-bottom: 8px;
        }

        .tool-btn:hover .tooltip {
            opacity: 1;
        }

        #main-canvas {
            display: block;
        }

        .color-picker {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            background: transparent;
        }

        .brush-size {
            width: 60px;
            height: 40px;
            cursor: pointer;
            background: transparent;
        }

        .loading {
            opacity: 0.5;
            pointer-events: none;
        }

        .room-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 8px 12px;
            color: white;
            outline: none;
            margin-bottom: 12px;
            width: 100%;
        }

        .room-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
    </style>
</head>
<body>
    <!-- Status Indicator -->
    <div class="status-indicator">
        <div class="status-dot" id="status-dot"></div>
        <span id="status-text">Offline</span>
    </div>

    <!-- Video Call Container -->
    <div class="video-container" id="video-container">
        <div class="video-header">
            <h4>Video Call</h4>
            <button class="tool-btn" id="close-video">
                <svg viewBox="0 0 24 24"><line x1="18" y1="6" x2="6" y2="18"/><line x1="6" y1="6" x2="18" y2="18"/></svg>
            </button>
        </div>
        <div class="video-content">
            <input type="text" class="room-input" id="room-input" placeholder="Enter room ID...">
            <video id="local-video" class="video-element" autoplay muted></video>
            <video id="remote-video" class="video-element" autoplay></video>
            <div class="video-controls">
                <button class="video-btn call" id="start-call">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20.01 15.38c-1.23 0-2.42-.2-3.53-.56a.977.977 0 0 0-1.01.24l-1.57 1.97c-2.83-1.35-5.48-3.9-6.89-6.83l1.95-1.66c.27-.28.35-.67.24-1.02-.37-1.11-.56-2.3-.56-3.53 0-.54-.45-.99-.99-.99H4.19C3.65 3 3 3.24 3 3.99 3 13.28 10.73 21 20.01 21c.71 0 .99-.63.99-1.18v-3.45c0-.54-.45-.99-.99-.99z"/>
                    </svg>
                </button>
                <button class="video-btn mute" id="mute-audio">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z"/>
                    </svg>
                </button>
                <button class="video-btn mute" id="mute-video">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z"/>
                    </svg>
                </button>
                <button class="video-btn end" id="end-call">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 9c-1.6 0-3.15.25-4.6.72v3.1c0 .39-.23.74-.56.9-.98.49-1.87 1.12-2.66 1.85-.18.18-.43.28-.7.28-.28 0-.53-.11-.71-.29L.29 13.08c-.18-.17-.29-.42-.29-.7 0-.28.11-.53.29-.71C3.34 8.78 7.46 7 12 7s8.66 1.78 11.71 4.67c.18.18.29.43.29.71 0 .28-.11.53-.29.7l-2.48 2.48c-.18.18-.43.29-.71.29-.27 0-.52-.1-.7-.28-.79-.73-1.68-1.36-2.66-1.85-.33-.16-.56-.51-.56-.9v-3.1C15.15 9.25 13.6 9 12 9z"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Toolbar -->
    <div class="toolbar">
        <button class="tool-btn" id="hand-btn" title="Hand">
            <svg viewBox="0 0 24 24"><path d="M18 11V6a2 2 0 0 0-4 0v5"/><path d="M14 10V4a2 2 0 0 0-4 0v2"/><path d="M10 10.5V6a2 2 0 0 0-4 0v8"/><path d="M18 8a2 2 0 1 1 4 0v6a8 8 0 0 1-8 8h-2c-2.8 0-4.5-.86-5.99-2.34l-3.6-3.6a2 2 0 0 1 2.83-2.83L7 15"/></svg>
            <div class="tooltip">Hand (H)</div>
        </button>

        <button class="tool-btn active" id="select-btn" title="Select">
            <svg viewBox="0 0 24 24"><path d="M3 3l7.07 16.97 2.51-7.39 7.39-2.51L3 3z"/></svg>
            <div class="tooltip">Select (V)</div>
        </button>

        <div class="divider"></div>

        <button class="tool-btn" id="rectangle-btn" title="Rectangle">
            <svg viewBox="0 0 24 24"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/></svg>
            <div class="tooltip">Rectangle (R)</div>
        </button>

        <button class="tool-btn" id="circle-btn" title="Circle">
            <svg viewBox="0 0 24 24"><circle cx="12" cy="12" r="10"/></svg>
            <div class="tooltip">Circle (C)</div>
        </button>

        <button class="tool-btn" id="line-btn" title="Line">
            <svg viewBox="0 0 24 24"><path d="M5 12h14"/></svg>
            <div class="tooltip">Line (L)</div>
        </button>

        <div class="divider"></div>

        <button class="tool-btn" id="pen-btn" title="Pen">
            <svg viewBox="0 0 24 24"><path d="M12 19l7-7 3 3-7 7-3-3z"/><path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z"/><path d="M2 2l7.586 7.586"/></svg>
            <div class="tooltip">Pen (P)</div>
        </button>

        <div class="divider"></div>

        <!-- Color Picker -->
        <input type="color" id="color-picker" value="#ffffff" class="color-picker" title="Color">

        <!-- Brush Size -->
        <input type="range" id="brush-size" min="1" max="20" value="2" class="brush-size" title="Brush Size">

        <div class="divider"></div>

        <button class="tool-btn" id="video-btn" title="Video Call">
            <svg viewBox="0 0 24 24"><path d="M23 7l-7 5 7 5V7z"/><rect x="1" y="5" width="15" height="14" rx="2" ry="2"/></svg>
            <div class="tooltip">Video Call</div>
        </button>

        <button class="tool-btn" id="chat-btn" title="AI Chat">
            <svg viewBox="0 0 24 24"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/></svg>
            <div class="tooltip">AI Chat</div>
        </button>
    </div>

    <!-- Canvas -->
    <div class="canvas-container">
        <canvas id="main-canvas"></canvas>
    </div>

    <!-- Chat Sidebar -->
    <div class="chat-sidebar" id="chat-sidebar">
        <div class="chat-header">
            <h3>AI Assistant</h3>
            <button class="tool-btn" id="close-chat">
                <svg viewBox="0 0 24 24"><line x1="18" y1="6" x2="6" y2="18"/><line x1="6" y1="6" x2="18" y2="18"/></svg>
            </button>
        </div>
        <div class="chat-messages" id="chat-messages">
            <div class="message">
                <strong>AI:</strong> Hello! I'm your AI assistant. I can help you with drawing, answer questions, and analyze your canvas. What would you like to create today?
            </div>
        </div>
        <div class="chat-input-area">
            <input type="text" class="chat-input" id="chat-input" placeholder="Ask AI anything...">
            <button class="screenshot-btn" id="screenshot-btn" title="Send Canvas Screenshot">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"/>
                    <circle cx="12" cy="13" r="4"/>
                </svg>
            </button>
            <button class="send-btn" id="send-btn">Send</button>
        </div>
    </div>

    <script>
        // Initialize Fabric.js canvas
        const canvas = new fabric.Canvas('main-canvas', {
            backgroundColor: '#000000',
            selection: true,
            isDrawingMode: false
        });

        // Resize canvas to full screen
        function resizeCanvas() {
            canvas.setDimensions({
                width: window.innerWidth,
                height: window.innerHeight
            });
            canvas.renderAll();
        }

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // Current tool state
        let currentTool = 'select';
        let isDrawing = false;
        let startX, startY;
        let currentShape = null;
        let currentColor = '#ffffff';
        let currentBrushSize = 2;
        let isPanning = false;
        let lastPanPoint = null;

        // Tool buttons
        const toolButtons = {
            'hand': document.getElementById('hand-btn'),
            'select': document.getElementById('select-btn'),
            'rectangle': document.getElementById('rectangle-btn'),
            'circle': document.getElementById('circle-btn'),
            'line': document.getElementById('line-btn'),
            'pen': document.getElementById('pen-btn'),
            'video': document.getElementById('video-btn'),
            'chat': document.getElementById('chat-btn')
        };

        // UI Elements
        const colorPicker = document.getElementById('color-picker');
        const brushSize = document.getElementById('brush-size');
        const statusDot = document.getElementById('status-dot');
        const statusText = document.getElementById('status-text');
        const chatSidebar = document.getElementById('chat-sidebar');
        const chatMessages = document.getElementById('chat-messages');
        const chatInput = document.getElementById('chat-input');
        const sendBtn = document.getElementById('send-btn');
        const screenshotBtn = document.getElementById('screenshot-btn');
        const closeChatBtn = document.getElementById('close-chat');
        const videoContainer = document.getElementById('video-container');
        const closeVideoBtn = document.getElementById('close-video');

        // Status management
        let isOnline = false;

        function updateStatus(online) {
            isOnline = online;
            statusDot.className = online ? 'status-dot online' : 'status-dot';
            statusText.textContent = online ? 'Online' : 'Offline';
        }

        // Check server status
        function checkServerStatus() {
            fetch('/health')
                .then(response => {
                    if (response.ok) {
                        updateStatus(true);
                        return response.json();
                    } else {
                        updateStatus(false);
                    }
                })
                .catch(() => updateStatus(false));
        }

        // Check status every 30 seconds
        checkServerStatus();
        setInterval(checkServerStatus, 30000);

        // Color picker and brush size
        colorPicker.addEventListener('change', (e) => {
            currentColor = e.target.value;
            if (canvas.isDrawingMode) {
                canvas.freeDrawingBrush.color = currentColor;
            }
        });

        brushSize.addEventListener('input', (e) => {
            currentBrushSize = parseInt(e.target.value);
            if (canvas.isDrawingMode) {
                canvas.freeDrawingBrush.width = currentBrushSize;
            }
        });

        // Set active tool
        function setActiveTool(tool) {
            currentTool = tool;

            // Update button states
            Object.values(toolButtons).forEach(btn => btn.classList.remove('active'));
            if (toolButtons[tool]) {
                toolButtons[tool].classList.add('active');
            }

            // Configure canvas
            switch(tool) {
                case 'select':
                    canvas.isDrawingMode = false;
                    canvas.selection = true;
                    canvas.defaultCursor = 'default';
                    canvas.hoverCursor = 'move';
                    break;
                case 'hand':
                    canvas.isDrawingMode = false;
                    canvas.selection = false;
                    canvas.defaultCursor = 'grab';
                    canvas.hoverCursor = 'grab';
                    break;
                case 'pen':
                    canvas.isDrawingMode = true;
                    canvas.selection = false;
                    canvas.freeDrawingBrush.width = currentBrushSize;
                    canvas.freeDrawingBrush.color = currentColor;
                    canvas.defaultCursor = 'crosshair';
                    break;
                case 'line':
                case 'rectangle':
                case 'circle':
                    canvas.isDrawingMode = false;
                    canvas.selection = false;
                    canvas.defaultCursor = 'crosshair';
                    canvas.hoverCursor = 'crosshair';
                    break;
            }
        }

        // Add tool button event listeners
        Object.entries(toolButtons).forEach(([tool, button]) => {
            button.addEventListener('click', () => {
                if (tool === 'chat') {
                    toggleChat();
                } else if (tool === 'video') {
                    toggleVideo();
                } else {
                    setActiveTool(tool);
                }
            });
        });

        // Hand tool panning functionality
        function startPanning(pointer) {
            if (currentTool === 'hand') {
                isPanning = true;
                lastPanPoint = { x: pointer.x, y: pointer.y };
                canvas.defaultCursor = 'grabbing';
            }
        }

        function updatePanning(pointer) {
            if (isPanning && lastPanPoint) {
                const deltaX = pointer.x - lastPanPoint.x;
                const deltaY = pointer.y - lastPanPoint.y;

                // Move all objects
                canvas.getObjects().forEach(obj => {
                    obj.left += deltaX;
                    obj.top += deltaY;
                    obj.setCoords();
                });

                canvas.renderAll();
                lastPanPoint = { x: pointer.x, y: pointer.y };
            }
        }

        function stopPanning() {
            if (isPanning) {
                isPanning = false;
                lastPanPoint = null;
                canvas.defaultCursor = 'grab';
            }
        }

        // Shape drawing functions
        function startShapeDrawing(pointer) {
            if (['line', 'rectangle', 'circle'].includes(currentTool)) {
                isDrawing = true;
                startX = pointer.x;
                startY = pointer.y;

                switch(currentTool) {
                    case 'line':
                        currentShape = new fabric.Line([startX, startY, startX, startY], {
                            stroke: currentColor,
                            strokeWidth: currentBrushSize,
                            selectable: false
                        });
                        break;
                    case 'rectangle':
                        currentShape = new fabric.Rect({
                            left: startX,
                            top: startY,
                            width: 0,
                            height: 0,
                            fill: 'transparent',
                            stroke: currentColor,
                            strokeWidth: currentBrushSize,
                            selectable: false
                        });
                        break;
                    case 'circle':
                        currentShape = new fabric.Circle({
                            left: startX,
                            top: startY,
                            radius: 0,
                            fill: 'transparent',
                            stroke: currentColor,
                            strokeWidth: currentBrushSize,
                            selectable: false
                        });
                        break;
                }

                if (currentShape) {
                    canvas.add(currentShape);
                }
            }
        }

        function updateShapeDrawing(pointer) {
            if (!isDrawing || !currentShape) return;

            switch(currentTool) {
                case 'line':
                    currentShape.set({ x2: pointer.x, y2: pointer.y });
                    break;
                case 'rectangle':
                    const width = pointer.x - startX;
                    const height = pointer.y - startY;
                    currentShape.set({
                        width: Math.abs(width),
                        height: Math.abs(height),
                        left: width < 0 ? pointer.x : startX,
                        top: height < 0 ? pointer.y : startY
                    });
                    break;
                case 'circle':
                    const radius = Math.sqrt(Math.pow(pointer.x - startX, 2) + Math.pow(pointer.y - startY, 2)) / 2;
                    currentShape.set({
                        radius: radius,
                        left: startX - radius,
                        top: startY - radius
                    });
                    break;
            }

            canvas.renderAll();
        }

        function finishShapeDrawing() {
            if (currentShape) {
                currentShape.set({ selectable: true });
            }
            isDrawing = false;
            currentShape = null;
        }

        // Canvas mouse events
        canvas.on('mouse:down', (options) => {
            if (currentTool === 'hand') {
                startPanning(options.pointer);
            } else if (['line', 'rectangle', 'circle'].includes(currentTool)) {
                startShapeDrawing(options.pointer);
            }
        });

        canvas.on('mouse:move', (options) => {
            if (currentTool === 'hand') {
                updatePanning(options.pointer);
            } else if (isDrawing) {
                updateShapeDrawing(options.pointer);
            }
        });

        canvas.on('mouse:up', () => {
            if (currentTool === 'hand') {
                stopPanning();
            } else if (isDrawing) {
                finishShapeDrawing();
            }
        });

        // Chat functionality
        function toggleChat() {
            chatSidebar.classList.toggle('open');
        }

        function addMessage(sender, message) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';
            messageDiv.innerHTML = `<strong>${sender}:</strong> ${message}`;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function sendMessage() {
            const message = chatInput.value.trim();
            if (!message) return;

            addMessage('You', message);
            chatInput.value = '';

            // Show loading state
            sendBtn.classList.add('loading');
            sendBtn.textContent = 'Sending...';

            // Send to AI backend
            fetch('/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ message: message })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                addMessage('AI', data.response || 'Sorry, I could not process your request.');
            })
            .catch(error => {
                console.error('Chat Error:', error);
                addMessage('AI', 'Sorry, I encountered an error. Please check if the server is running and try again.');
            })
            .finally(() => {
                sendBtn.classList.remove('loading');
                sendBtn.textContent = 'Send';
            });
        }

        function sendScreenshot() {
            // Show loading state
            screenshotBtn.classList.add('loading');

            try {
                // Get canvas as image data
                const canvasDataURL = canvas.toDataURL('image/png');

                // Send screenshot to AI
                fetch('/analyze_canvas', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        image: canvasDataURL,
                        message: "Please analyze this canvas drawing and provide feedback or suggestions."
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    addMessage('AI', data.response || 'I analyzed your canvas but could not provide feedback.');
                    if (!chatSidebar.classList.contains('open')) {
                        toggleChat();
                    }
                })
                .catch(error => {
                    console.error('Screenshot Error:', error);
                    addMessage('AI', 'Sorry, I could not analyze the canvas. Please check if the server is running and try again.');
                    if (!chatSidebar.classList.contains('open')) {
                        toggleChat();
                    }
                })
                .finally(() => {
                    screenshotBtn.classList.remove('loading');
                });
            } catch (error) {
                console.error('Canvas Error:', error);
                addMessage('AI', 'Sorry, I could not capture the canvas. Please try again.');
                screenshotBtn.classList.remove('loading');
            }
        }

        // Video calling functionality
        let localStream = null;
        let remoteStream = null;
        let peerConnection = null;
        let isCallActive = false;

        const localVideo = document.getElementById('local-video');
        const remoteVideo = document.getElementById('remote-video');
        const roomInput = document.getElementById('room-input');
        const startCallBtn = document.getElementById('start-call');
        const endCallBtn = document.getElementById('end-call');
        const muteAudioBtn = document.getElementById('mute-audio');
        const muteVideoBtn = document.getElementById('mute-video');

        // WebRTC configuration
        const rtcConfig = {
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' },
                { urls: 'stun:stun1.l.google.com:19302' }
            ]
        };

        function toggleVideo() {
            videoContainer.style.display = videoContainer.style.display === 'none' ? 'block' : 'none';
        }

        async function startCall() {
            try {
                // Get user media
                localStream = await navigator.mediaDevices.getUserMedia({
                    video: true,
                    audio: true
                });

                localVideo.srcObject = localStream;

                // Create peer connection
                peerConnection = new RTCPeerConnection(rtcConfig);

                // Add local stream to peer connection
                localStream.getTracks().forEach(track => {
                    peerConnection.addTrack(track, localStream);
                });

                // Handle remote stream
                peerConnection.ontrack = (event) => {
                    remoteStream = event.streams[0];
                    remoteVideo.srcObject = remoteStream;
                };

                // Handle ICE candidates
                peerConnection.onicecandidate = (event) => {
                    if (event.candidate) {
                        console.log('ICE candidate:', event.candidate);
                        // In a real app, send this to the other peer via signaling server
                    }
                };

                isCallActive = true;
                startCallBtn.style.display = 'none';
                endCallBtn.style.display = 'block';

                console.log('Call started successfully');

            } catch (error) {
                console.error('Error starting call:', error);
                alert('Could not start call. Please check camera/microphone permissions.');
            }
        }

        function endCall() {
            if (localStream) {
                localStream.getTracks().forEach(track => track.stop());
                localStream = null;
            }

            if (peerConnection) {
                peerConnection.close();
                peerConnection = null;
            }

            localVideo.srcObject = null;
            remoteVideo.srcObject = null;

            isCallActive = false;
            startCallBtn.style.display = 'block';
            endCallBtn.style.display = 'none';

            console.log('Call ended');
        }

        function toggleAudio() {
            if (localStream) {
                const audioTrack = localStream.getAudioTracks()[0];
                if (audioTrack) {
                    audioTrack.enabled = !audioTrack.enabled;
                    muteAudioBtn.style.background = audioTrack.enabled ? 'rgba(255, 255, 255, 0.1)' : '#ef4444';
                }
            }
        }

        function toggleVideoStream() {
            if (localStream) {
                const videoTrack = localStream.getVideoTracks()[0];
                if (videoTrack) {
                    videoTrack.enabled = !videoTrack.enabled;
                    muteVideoBtn.style.background = videoTrack.enabled ? 'rgba(255, 255, 255, 0.1)' : '#ef4444';
                }
            }
        }

        // Event listeners
        sendBtn.addEventListener('click', sendMessage);
        screenshotBtn.addEventListener('click', sendScreenshot);

        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                sendMessage();
            }
        });

        closeChatBtn.addEventListener('click', () => {
            chatSidebar.classList.remove('open');
        });

        closeVideoBtn.addEventListener('click', () => {
            videoContainer.style.display = 'none';
        });

        startCallBtn.addEventListener('click', startCall);
        endCallBtn.addEventListener('click', endCall);
        muteAudioBtn.addEventListener('click', toggleAudio);
        muteVideoBtn.addEventListener('click', toggleVideoStream);

        // Keyboard shortcuts - FIXED to not interfere with text input
        document.addEventListener('keydown', (e) => {
            // Don't trigger shortcuts when typing in input fields or text areas
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.isContentEditable) {
                return;
            }

            // Don't trigger shortcuts when editing text on canvas
            if (canvas.getActiveObject() && canvas.getActiveObject().isEditing) {
                return;
            }

            switch(e.key.toLowerCase()) {
                case 'v':
                    e.preventDefault();
                    setActiveTool('select');
                    break;
                case 'h':
                    e.preventDefault();
                    setActiveTool('hand');
                    break;
                case 'p':
                    e.preventDefault();
                    setActiveTool('pen');
                    break;
                case 'l':
                    e.preventDefault();
                    setActiveTool('line');
                    break;
                case 'r':
                    e.preventDefault();
                    setActiveTool('rectangle');
                    break;
                case 'c':
                    e.preventDefault();
                    setActiveTool('circle');
                    break;
                case 'escape':
                    e.preventDefault();
                    setActiveTool('select');
                    chatSidebar.classList.remove('open');
                    videoContainer.style.display = 'none';
                    break;
            }
        });

        // Initialize with select tool
        setActiveTool('select');

        console.log('FluxDraw initialized successfully!');
        console.log('Available tools:', Object.keys(toolButtons));
        console.log('Keyboard shortcuts: V=Select, H=Hand, P=Pen, L=Line, R=Rectangle, C=Circle');
        console.log('Features: AI Chat, Canvas Screenshot Analysis, Video Calling, Online/Offline Status');
    </script>
</body>
</html>
