from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import google.generativeai as genai
import os
import base64
import io
import matplotlib
matplotlib.use('Agg') # Use Agg backend for non-interactive plotting
import matplotlib.pyplot as plt
import traceback

app = Flask(__name__)
CORS(app) # Enable CORS for all routes

# IMPORTANT: Replace with your actual API key if this is a placeholder, 
# or ensure it's set as an environment variable for security.
API_KEY = "AIzaSyBdH-Gig7TYSJvT8eGpi8dDtGMGtoY1tTE" # User Provided Key
MODEL_NAME = "gemini-1.5-flash-latest" 
generative_model = None

PRIMARY_LATEX_INSTRUCTION = (
    "CRITICAL FORMATTING RULE: For ALL mathematical expressions, equations, variables, formulas, "
    "and symbols in your response, you MUST use LaTeX formatting. "
    "Use `$$...$$` for display mathematics (equations on their own line, centered). "
    "Use `$...$` for inline mathematics (math within a sentence). "
    "For example, write `$x^2$` instead of x^2, and `$$\\frac{a}{b}$$` instead of a/b on its own line. "
    "Ensure your entire response, including all explanations and step-by-step solutions, adheres to this rule strictly. "
    "Do not use HTML tags like <sup> for math."
)

try:
    genai.configure(api_key=API_KEY)
    generative_model = genai.GenerativeModel(MODEL_NAME)
    print(f"Successfully initialized Gemini model: {MODEL_NAME}")
except Exception as e:
    print(f"Error initializing Gemini model ({MODEL_NAME}): {e}")
    # Consider how to handle this - app might not be fully functional
    # For now, it will print the error and continue, endpoints will fail if model is None.

@app.route('/')
def serve_index():
    return send_from_directory('.', 'frontend.html')

@app.route('/health')
def health_check():
    return jsonify({"status": "online", "model": "gemini-1.5-flash-latest"})

@app.route('/interpret', methods=['POST'])
def interpret_image_or_text():
    if not generative_model:
        return jsonify({"error": "Gemini model not initialized. Check server logs."}), 500
    
    data = request.json
    user_prompt = data.get('prompt', "Please analyze the provided content and image (if any).")
    # image_data_base64 should now be a string (the base64 part only) or None
    image_data_base64 = data.get('image_data') 

    final_prompt_to_ai = f"{PRIMARY_LATEX_INSTRUCTION}\n\nUser question/request: {user_prompt}"
    
    ai_response_text = ""
    python_code_suggestion = None

    try:
        content_parts = []
        content_parts.append(final_prompt_to_ai) 

        if image_data_base64:
            # This check is still good for robustness, but type should be str now
            if not isinstance(image_data_base64, str): 
                print(f"Error: image_data_base64 received was not a string, it was: {type(image_data_base64)}")
                # This indicates an issue with frontend still, or unexpected data
                return jsonify({"error": "Internal server error: Image data format incorrect on server."}), 500
            
            try:
                image_bytes = base64.b64decode(image_data_base64)
                image_part = {"mime_type": "image/png", "data": image_bytes}
                content_parts.append(image_part)
            except base64.binascii.Error as b64_error:
                print(f"Base64 decoding error: {b64_error}")
                return jsonify({"error": f"Invalid Base64 image data: {b64_error}"}), 400
            
        # Generate content
        response = generative_model.generate_content(content_parts, stream=False)
        response.resolve() # Ensure all parts are resolved if stream=False was not enough
        
        raw_text_from_ai = ""
        # Extract text from response
        if response.parts:
            raw_text_from_ai = "".join(part.text for part in response.parts if hasattr(part, 'text') and part.text is not None)
        elif hasattr(response, 'text') and response.text is not None:
            raw_text_from_ai = response.text
        
        if not raw_text_from_ai: # Check if AI returned any text
            if response.prompt_feedback and response.prompt_feedback.block_reason:
                ai_response_text = f"Content blocked by AI: {response.prompt_feedback.block_reason_message or response.prompt_feedback.block_reason}"
            elif hasattr(response, 'candidates') and response.candidates and response.candidates[0].finish_reason != 'STOP':
                ai_response_text = f"AI finished with reason: {response.candidates[0].finish_reason}. No text content."
            else:
                ai_response_text = "AI returned no textual content or the content might have been blocked."
        else:
            ai_response_text = raw_text_from_ai

        # **** CORRECTED PYTHON CODE EXTRACTION ****
        # Attempt to extract python code block
        py_code_marker = "```python"
        code_parts = ai_response_text.split(py_code_marker)
        
        if len(code_parts) > 1:
            text_before_code = code_parts
            # The rest of the string after the first ```python
            code_and_after_text = code_parts[1] 
            
            # Split out the code block itself (ends with ```)
            end_code_marker = "```"
            code_block_parts = code_and_after_text.split(end_code_marker, 1)
            
            if len(code_block_parts) > 0:
                python_code_suggestion = code_block_parts[0].strip() # This is the code
            
            text_after_code = ""
            if len(code_block_parts) > 1:
                text_after_code = code_block_parts[1] # Text after the code block
            
            # Reconstruct ai_response_text without the code block
            ai_response_text = (text_before_code + text_after_code).strip()
        # If no "```python" block, python_code_suggestion remains None
        
        return jsonify({
            "text": ai_response_text,
            "python_code": python_code_suggestion
        })

    except Exception as e:
        error_message = f"AI API Error: {str(e)}"
        # More specific error for base64 issues if not caught earlier
        if isinstance(e, base64.binascii.Error): # Should be caught above, but as a fallback
            error_message = f"AI API Error: Invalid Base64 image data. {str(e)}"
        elif hasattr(e, 'message') and e.message: # Some Google API errors have a .message attribute
            error_message = f"AI API Error: {e.message}"
        
        print(f"Error during Gemini API call: {traceback.format_exc()}")
        return jsonify({"error": error_message}), 500

@app.route('/execute_python', methods=['POST'])
def execute_python():
    data = request.json
    code = data.get('code')

    if not code:
        return jsonify({"error": "No code provided"}), 400

    # Restricted environment for exec
    # Add more safe builtins or modules as needed (e.g., math, numpy if installed)
    # Be very cautious with what you allow here.
    exec_globals = {
        'plt': plt,
        '__builtins__': {
            'print': print, 'range': range, 'len': len, 'Exception': Exception, 
            'list': list, 'dict': dict, 'str': str, 'int': int, 'float': float, 
            'True': True, 'False': False, 'None': None, 'abs': abs, 'round':round, 
            'sum':sum, 'min':min, 'max':max, 'pow':pow, 'enumerate':enumerate, 
            'zip':zip, 'sorted':sorted, 'map':map, 'filter':filter
            # Consider adding 'math' module if commonly used by AI and safe.
        }
    }
    local_vars = {} # To capture any variables defined by the exec'd code if needed
    
    # Capture stdout to send back to client if needed (more complex)
    # For now, relying on matplotlib plots as primary visual output

    try:
        # Execute the code
        exec(code, exec_globals, local_vars)
        
        image_base64 = None
        # Check if matplotlib was used to create a figure
        if plt.get_fignums(): # Check if there are any active figures
            fig = plt.gcf() # Get current figure
            if fig.axes: # Check if the figure actually has content (axes)
                buf = io.BytesIO()
                plt.savefig(buf, format='png', bbox_inches='tight')
                plt.close(fig) # Close the figure to free memory
                buf.seek(0)
                image_base64 = base64.b64encode(buf.getvalue()).decode('utf-8')
            else:
                plt.close(fig) # Close empty figure
        
        if image_base64:
            return jsonify({"output_type": "image", "data": image_base64})
        else:
            # If no image, perhaps there was text output (not captured by default)
            # Or the code ran successfully without producing a plottable output.
            return jsonify({"output_type": "text", "data": "Code executed. No visual output generated or captured."})

    except Exception as e:
        print(f"Error executing Python code: {traceback.format_exc()}")
        # Ensure all figures are closed on error too
        for fignum in plt.get_fignums():
            plt.close(plt.figure(fignum))
        return jsonify({"error": f"Python Execution Error: {str(e)}"}), 500
    finally:
        # Ensure all matplotlib figures are closed to prevent memory leaks
        plt.close('all')

@app.route('/analyze_canvas', methods=['POST'])
def analyze_canvas():
    if not generative_model:
        return jsonify({"error": "Gemini model not initialized. Check server logs."}), 500

    try:
        data = request.json
        image_data = data.get('image', '')
        user_message = data.get('message', 'Please analyze this canvas drawing.')

        if not image_data:
            return jsonify({"error": "No image data provided"}), 400

        # Remove data URL prefix if present
        if image_data.startswith('data:image'):
            image_data = image_data.split(',')[1]

        # Decode base64 image
        image_bytes = base64.b64decode(image_data)

        # Create image part for Gemini
        image_part = {
            "mime_type": "image/png",
            "data": image_bytes
        }

        # Create prompt for canvas analysis
        prompt = f"""
        {PRIMARY_LATEX_INSTRUCTION}

        You are an AI assistant helping with canvas analysis and drawing feedback.
        The user has shared a canvas drawing and asked: "{user_message}"

        Please analyze the drawing and provide helpful feedback, suggestions, or answer their question.
        Be encouraging and constructive in your response.
        If you see mathematical content, use proper LaTeX formatting.
        """

        # Generate response with image
        response = generative_model.generate_content([prompt, image_part])

        return jsonify({"response": response.text})

    except Exception as e:
        print(f"Error in canvas analysis: {traceback.format_exc()}")
        return jsonify({"error": f"Canvas analysis error: {str(e)}"}), 500

@app.route('/chat', methods=['POST'])
def chat():
    if not generative_model:
        return jsonify({"error": "Gemini model not initialized. Check server logs."}), 500

    try:
        data = request.json
        user_message = data.get('message', '')

        if not user_message:
            return jsonify({"error": "No message provided"}), 400

        # Create prompt for general chat
        prompt = f"""
        {PRIMARY_LATEX_INSTRUCTION}

        You are an AI assistant helping with drawing, design, and creative tasks.
        The user asked: "{user_message}"

        Please provide a helpful, encouraging response. If discussing mathematical concepts,
        use proper LaTeX formatting. Keep responses concise but informative.
        """

        # Generate response
        response = generative_model.generate_content(prompt)

        return jsonify({"response": response.text})

    except Exception as e:
        print(f"Error in chat: {traceback.format_exc()}")
        return jsonify({"error": f"Chat error: {str(e)}"}), 500


if __name__ == '__main__':
    if not generative_model:
        print("\nWARNING: Gemini model failed to initialize. AI features will not work.\n")
    app.run(debug=True, host='0.0.0.0', port=5002)